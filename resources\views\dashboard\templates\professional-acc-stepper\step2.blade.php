<div class="container form-card">
    <div class="row">
        <div class="col-12 mb-5 px-0">
            <h2 class="fs-title">What services do you offer?</h2>
            <p>Choose up to 3 categories by selecting their subcategories (max 3 subcategories per category)</p>
        </div>
    </div>

    <!-- Hidden inputs for selected subcategories only -->
    <div id="selected-subcategories-inputs"></div>


    <div class="col-md-12">
        @foreach ($categories->chunk(4) as $categoryChunk)
            <div class="row parent_services row g-3 align-items-stretch">
                {{-- CATEGORIES — 4 columns --}}


                    @foreach ($categoryChunk as $category)
                    <div class="col-md-3 mb-4 service_category d-flex" data-ctg="{{ $category->id }}">
                        <label class="custom-radio w-100">
                        <div class="card radio-box h-100 w-100 d-flex flex-column text-center">
                            <div class="card-body d-flex flex-column justify-content-center align-items-center">
                                <img src="{{ ($category->image ? asset('website/'.$category->image) : asset('placeholder.png')) }}"
                                    alt="icon" class="service-icon mb-2" style="height:40px;">
                                <div class="fw-semibold mt-1">{{ $category->name ?? '' }}</div>
                            </div>
                        </div>
                        </label>
                    </div>
                    @endforeach





                <!-- @foreach ($categoryChunk as $category)
                    <div class="col-md-3 mb-4 service_category" data-ctg="{{ $category->id }}">
                        <div class="services-section custom-radio-group ">
                            <label class="custom-radio w-100 card-grid">
                                <span class="radio-box position-relative d-flex flex-column justify-content-center p-7 text-center card h-100">
                                    <img src="{{ asset('website') . '/' . $category->image ?? '' }}" alt="icon" class="service-icon mb-2" style="height: 40px;">
                                    <span class="label-text fw-semibold text-start card-body ">{{ $category->name ?? '' }}</span>
                                    
                                </span>
                            </label>
                        </div>
                    </div>
                @endforeach -->

                {{-- SUBCATEGORIES — Full width under row --}}
                <div class="col-md-12 sub_categories">
                    <div class="">
                        @foreach ($categoryChunk as $category)
                            <div class=" sub_category" id="sub_category_{{ $category->id }}" style="display: none;">
                                @if ($category->subcategories->isNotEmpty())
                                    <div class="custom-checkbox-group d-flex flex-wrap gap-2">
                                        @foreach ($category->subcategories as $subcategory)
                                            <label class="custom-checkbox rounded-pill px-4 py-2 border d-flex align-items-center">
                                                <input type="checkbox" name="subcategories[]"
                                                    value="{{ $subcategory->id }}"
                                                    @if (in_array($subcategory->id, old('subcategories', auth()->user()->subcategories->pluck('id')->toArray()))) checked @endif>
                                                <span class="checkbox-label ms-2">{{ $subcategory->name ?? '' }}</span>
                                            </label>
                                        @endforeach
                                    </div>
                                @else
                                    <p class="m-0">No subcategories available</p>
                                @endif
                            </div>
                        @endforeach
                    </div>
                </div>
            </div>
        @endforeach
    </div>


        <!-- <div class="col-md-12">
            @foreach ($categories->chunk(3) as $categoryChunk)
                <div class="row parent_services">
                    @foreach ($categoryChunk as $category)
                        <div class="col-md-4 mb-8 service_category" data-ctg="{{ $category->id }}">
                            <div class="services-section custom-radio-group">
                                <label class="custom-radio">
                                    <span class="radio-box">
                                        <img src="{{ asset('website') . '/' . $category->image ?? '' }}" alt="icon"   class="service-icon">
                                        <span class="label-text">{{ $category->name ?? '' }}</span>
                                    </span>
                                </label>
                            </div>
                        </div>
                    @endforeach
                    <div class="col-md-12 sub_categories">
                        <div class="card">
                            @foreach ($categoryChunk as $category)
                                @if ($category->subcategories->isNotEmpty())
                                    <div class="sub_category" id="sub_category_{{ $category->id }}"
                                        data-sub-ctg="{{ $category->id }}">
                                        <div class="custom-checkbox-group my-5">
                                            @foreach ($category->subcategories as $subcategory)
                                                <label class="custom-checkbox">
                                                    <input type="checkbox" name="subcategories[]"
                                                        value="{{ $subcategory->id }}"
                                                        @if (in_array($subcategory->id, old('subcategories', auth()->user()->subcategories->pluck('id')->toArray()))) checked @endif>
                                                    <span class="checkbox-label">{{ $subcategory->name ?? '' }}</span>
                                                </label>
                                            @endforeach
                                        </div>
                                    </div>
                                @else
                                    <div class="sub_category" id="sub_category_{{ $category->id }}"
                                        data-sub-ctg="{{ $category->id }}">
                                        <p class="m-0">No subcategories available</p>
                                    </div>
                                @endif
                            @endforeach
                        </div>
                    </div>
                </div>
            @endforeach
        </div> -->

</div>
